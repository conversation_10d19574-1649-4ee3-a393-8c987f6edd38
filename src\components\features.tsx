import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { 
  Shield, 
  Zap, 
  Smartphone, 
  Clock, 
  Eye, 
  Lock 
} from "lucide-react"

const features = [
  {
    icon: Shield,
    title: "Bank-Grade Security",
    description: "Your transactions are protected with the same security standards used by major banks worldwide.",
  },
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Complete transactions in seconds, not minutes. Our optimized interface ensures speed without complexity.",
  },
  {
    icon: Smartphone,
    title: "Works Offline",
    description: "No internet? No problem. Access essential banking services even without data connectivity.",
  },
  {
    icon: Clock,
    title: "24/7 Availability",
    description: "Bank anytime, anywhere. Our services are available round the clock, every day of the year.", 
  },
  {
    icon: Eye,
    title: "User-Friendly Design",
    description: "Clean, intuitive interface that hides technical complexity behind beautiful, simple interactions.",
  },
  {
    icon: Lock,
    title: "Privacy Focused",
    description: "Your personal information and transaction data are encrypted and never shared with third parties.",
  }
]

export function Features() {
  return (
    <section id="features" className="py-20 lg:py-32 bg-muted/30">
      <div className="container">
        <div className="text-center space-y-6 mb-16">
          <h2 className="text-3xl lg:text-5xl font-bold">
            Why Choose OfflineBank?
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Built with modern technology principles while maintaining compatibility 
            with traditional banking infrastructure.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index}
              className="border-0 bg-background/60 backdrop-blur-sm hover:bg-background/80 transition-colors"
            >
              <CardHeader>
                <div className="p-3 rounded-xl bg-primary/10 w-fit">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}