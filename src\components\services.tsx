import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  CreditCard, 
  DollarSign, 
  Smartphone, 
  TrendingUp, 
  Users, 
  Zap 
} from "lucide-react"

const services = [
  {
    icon: CreditCard,
    title: "Check Balance",
    description: "View your account balance instantly without dialing complex codes",
    ussd: "*123#",
    gradient: "from-blue-500/10 to-blue-600/10"
  },
  {
    icon: DollarSign,
    title: "Transfer Money",
    description: "Send money to friends and family with a simple, secure interface",
    ussd: "*123*1#",
    gradient: "from-green-500/10 to-green-600/10"
  },
  {
    icon: Smartphone,
    title: "Buy Airtime",
    description: "Top up your phone or others' phones effortlessly",
    ussd: "*123*2#",
    gradient: "from-purple-500/10 to-purple-600/10"
  },
  {
    icon: TrendingUp,
    title: "View Statement",
    description: "Access your transaction history and account statements",
    ussd: "*123*3#",
    gradient: "from-orange-500/10 to-orange-600/10"
  },
  {
    icon: Users,
    title: "Pay Bills",
    description: "Settle utilities, subscriptions, and other bills quickly",
    ussd: "*123*4#",
    gradient: "from-pink-500/10 to-pink-600/10"
  },
  {
    icon: Zap,
    title: "Quick Loans",
    description: "Apply for instant micro-loans when you need them most",
    ussd: "*123*5#",
    gradient: "from-yellow-500/10 to-yellow-600/10"
  }
]

export function Services() {
  return (
    <section id="services" className="py-20 lg:py-32">
      <div className="container">
        <div className="text-center space-y-6 mb-16">
          <h2 className="text-3xl lg:text-5xl font-bold">
            Everything You Need
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Access all your banking services through our clean, intuitive interface. 
            No more remembering complex USSD codes.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className="group relative overflow-hidden border-0 bg-gradient-card shadow-card hover:shadow-card-hover transition-smooth cursor-pointer"
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-100 transition-opacity`} />
              
              <CardHeader className="relative">
                <div className="flex items-center justify-between">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <service.icon className="h-6 w-6 text-primary" />
                  </div>
                  <span className="text-xs font-mono text-muted-foreground opacity-60">
                    {service.ussd}
                  </span>
                </div>
                <CardTitle className="text-xl">{service.title}</CardTitle>
                <CardDescription className="text-base">
                  {service.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="relative">
                <Button variant="banking" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                  Use Service
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}