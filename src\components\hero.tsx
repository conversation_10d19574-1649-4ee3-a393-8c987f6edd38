import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Shield, Smartphone } from "lucide-react"
import heroImage from "@/assets/banking-hero.jpg"

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-gradient-hero py-20 lg:py-32">
      <div className="container relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="flex flex-col space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center rounded-full border px-3 py-1 text-sm">
                <Shield className="mr-2 h-4 w-4" />
                Secure Offline Banking
              </div>
              
              <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
                Banking Made
                <span className="block text-primary">Simple & Offline</span>
              </h1>
              
              <p className="text-xl text-muted-foreground max-w-lg">
                Experience seamless banking without internet connectivity. 
                Our intuitive interface transforms complex USSD codes into 
                simple, elegant interactions.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="hero" size="lg" className="flex items-center">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </div>

            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Smartphone className="mr-2 h-4 w-4" />
                Works on any phone
              </div>
              <div className="flex items-center">
                <Shield className="mr-2 h-4 w-4" />
                Bank-grade security
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="absolute inset-0 bg-gradient-primary rounded-3xl blur-3xl opacity-20" />
            <img 
              src={heroImage} 
              alt="Modern banking interface" 
              className="relative rounded-3xl shadow-hero w-full h-auto"
            />
          </div>
        </div>
      </div>
    </section>
  )
}