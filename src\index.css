@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 220 13% 13%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 13%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 13%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 100% 68%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 220 13% 13%;

    --muted: 210 40% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    /* Banking theme colors */
    --banking-blue: 217 91% 60%;
    --banking-blue-light: 217 100% 68%;
    --banking-green: 142 76% 36%;
    --banking-navy: 220 39% 11%;
    --banking-gray: 220 9% 46%;
    --banking-light: 210 20% 98%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--banking-blue)), hsl(var(--banking-blue-light)));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100% / 0.8), hsl(0 0% 100% / 0.4));
    --gradient-hero: linear-gradient(135deg, hsl(var(--banking-blue) / 0.1), hsl(var(--banking-blue-light) / 0.05));

    /* Shadows */
    --shadow-card: 0 4px 20px -2px hsl(var(--banking-blue) / 0.1);
    --shadow-card-hover: 0 8px 30px -4px hsl(var(--banking-blue) / 0.15);
    --shadow-hero: 0 20px 40px -12px hsl(var(--banking-blue) / 0.25);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 39% 11%;
    --foreground: 210 40% 98%;

    --card: 220 33% 15%;
    --card-foreground: 210 40% 98%;

    --popover: 220 33% 15%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 100% 68%;

    --secondary: 220 26% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 26% 20%;
    --muted-foreground: 220 9% 65%;

    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 26% 20%;
    --input: 220 26% 20%;
    --ring: 217 91% 60%;

    /* Banking theme colors - dark mode */
    --banking-blue: 217 91% 60%;
    --banking-blue-light: 217 100% 68%;
    --banking-green: 142 76% 36%;
    --banking-navy: 220 39% 11%;
    --banking-gray: 220 9% 65%;
    --banking-light: 220 33% 15%;

    /* Gradients - dark mode */
    --gradient-primary: linear-gradient(135deg, hsl(var(--banking-blue)), hsl(var(--banking-blue-light)));
    --gradient-card: linear-gradient(145deg, hsl(220 33% 15% / 0.8), hsl(220 26% 20% / 0.4));
    --gradient-hero: linear-gradient(135deg, hsl(var(--banking-blue) / 0.2), hsl(var(--banking-blue-light) / 0.1));

    /* Shadows - dark mode */
    --shadow-card: 0 4px 20px -2px hsl(0 0% 0% / 0.3);
    --shadow-card-hover: 0 8px 30px -4px hsl(0 0% 0% / 0.4);
    --shadow-hero: 0 20px 40px -12px hsl(0 0% 0% / 0.5);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}