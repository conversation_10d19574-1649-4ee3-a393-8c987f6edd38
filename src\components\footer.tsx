import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Mail, Phone, MapPin } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-muted/50 border-t">
      <div className="container py-16">
        <div className="grid lg:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-primary" />
              <span className="text-xl font-bold">OfflineBank</span>
            </div>
            <p className="text-muted-foreground max-w-xs">
              Making banking accessible to everyone, everywhere. 
              Simple, secure, and always available.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold">Services</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-foreground transition-colors">Check Balance</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Transfer Money</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Buy Airtime</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Pay Bills</a></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold">Support</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-foreground transition-colors">Help Center</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Contact Us</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors">Terms of Service</a></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold">Stay Updated</h3>
            <p className="text-sm text-muted-foreground">
              Get the latest updates on new features and services.
            </p>
            <div className="flex space-x-2">
              <Input placeholder="Enter your email" className="flex-1" />
              <Button variant="default" size="sm">
                Subscribe
              </Button>
            </div>
          </div>
        </div>

        <div className="border-t mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Phone className="mr-2 h-4 w-4" />
                +****************
              </div>
              <div className="flex items-center">
                <Mail className="mr-2 h-4 w-4" />
                <EMAIL>
              </div>
              <div className="flex items-center">
                <MapPin className="mr-2 h-4 w-4" />
                New York, NY
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground">
              © 2024 OfflineBank. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}